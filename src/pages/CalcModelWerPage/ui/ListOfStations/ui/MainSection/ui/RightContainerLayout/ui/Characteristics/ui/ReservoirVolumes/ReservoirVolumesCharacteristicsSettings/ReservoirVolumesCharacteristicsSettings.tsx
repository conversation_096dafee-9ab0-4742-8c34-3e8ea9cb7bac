import { FormControlLabel, Radio, RadioGroup } from '@mui/material'
import { IReservoirVolumeSettings } from 'entities/api/calcModelWerManager.entities'
import { observer } from 'mobx-react'
import { useEffect, useState } from 'react'
import { But<PERSON> } from 'shared/ui/Button'
import { LoadingButton } from 'shared/ui/LoadingButton'
import { Modal } from 'shared/ui/Modal'
import { Row } from 'shared/ui/Row'
import { Switch } from 'shared/ui/Switch'
import { TextField } from 'shared/ui/TextField'
import { useStore } from 'stores/useStore'

import { getFormattedUnit } from '../../../lib'
import cls from './ReservoirVolumesCharacteristicsSettings.module.scss'
import { OffsettedTooltip } from './ui/OffsetedTooltip'

export const ReservoirVolumesCharacteristicsSettings = observer(() => {
  const {
    calcModelWerStore: {
      formattedDate,
      isSelectedDateEditable,
      selectedPlant,
      isSelectedPlantViewOnly,
      listOfStationsStore: {
        characteristicsStore: {
          editMode,
          reservoirVolumeStore: {
            getReservoirVolumeCharacteristics,
            saveReservoirVolumeSettings,
            currentSettings,
            isSettingsModified,
            isSettingsSaving,
            lastFilledRow,
            resetSettings,
            setRowCount,
            setUnit,
            setLevelAllowableChange,
            setVolumeAllowableChange,
            setPolynom,
            setMethod,
            setEmptyValuesValidation,
            setAllowableChangeValidation,
            setMonotonyValidation,
            isEditRows,
          },
        },
      },
    },
  } = useStore()

  const [isOpenCharacteristicsSettingsModal, setIsOpenCharacteristicsSettingsModal] = useState(false)

  const handleSaveCharacteristicsSettings = async () => {
    if (!selectedPlant || !currentSettings) return

    try {
      await saveReservoirVolumeSettings(selectedPlant.plantId, formattedDate, currentSettings)
      setIsOpenCharacteristicsSettingsModal(false)
    } catch (error) {
      console.error(error)
    }
  }

  useEffect(() => {
    if (!selectedPlant) return

    getReservoirVolumeCharacteristics(selectedPlant.plantId, formattedDate)
  }, [selectedPlant, formattedDate])

  if (!currentSettings) return null

  const isSaveButtonDisabled =
    !isSettingsModified || currentSettings.polynom.some((value) => Number.isNaN(Number(value)))

  const isOpenCharacteristicsSettingsModalButtonDisabled =
    !editMode || !isSelectedDateEditable || isSelectedPlantViewOnly || isEditRows

  const unit = getFormattedUnit(currentSettings.unit)

  return (
    <div>
      <Button
        variant='outlined'
        onClick={() => setIsOpenCharacteristicsSettingsModal(true)}
        disabled={isOpenCharacteristicsSettingsModalButtonDisabled}
      >
        Настройка характеристики
      </Button>

      <Modal
        open={isOpenCharacteristicsSettingsModal}
        maxWidth='xl'
        title='Настройка характеристики'
        onClose={() => {
          setIsOpenCharacteristicsSettingsModal(false)
          resetSettings()
        }}
        actions={
          <div className={cls.actionContainer}>
            <LoadingButton
              variant='contained'
              loading={isSettingsSaving}
              disabled={isSaveButtonDisabled}
              onClick={handleSaveCharacteristicsSettings}
              className={cls.saveButton}
            >
              Сохранить
            </LoadingButton>
          </div>
        }
      >
        <div className={cls.body}>
          <h2 className={cls.titleStation}>{selectedPlant!.name}</h2>
          <Row
            label={
              <div className={cls.polynomRow}>
                <p>Количество строк</p>
                <OffsettedTooltip
                  title={<span>Последняя заполненная строка: {lastFilledRow}</span>}
                  offset={[100, -65]}
                />
              </div>
            }
          >
            <TextField
              type='number'
              className={cls.rowCountTextField}
              numberOption={{ isInteger: true, lengthBeforeComma: 5 }}
              value={Number.isNaN(Number(currentSettings.rowCount)) ? '' : currentSettings.rowCount}
              onChange={(e) => setRowCount(e.target.value)}
            />
          </Row>
          <Row label='Ед. изм. объёма водохранилища'>
            <RadioGroup
              row
              value={currentSettings.unit ?? 'KM3'}
              onChange={(e) => setUnit(e.target.value as IReservoirVolumeSettings['unit'])}
            >
              <FormControlLabel value='KM3' control={<Radio />} label='км³' />
              <FormControlLabel value='MLN_M3' control={<Radio />} label='млн.м³' />
            </RadioGroup>
          </Row>
          <Row label='Валидация пустых значений'>
            <Switch
              checked={currentSettings.emptyValuesValidation ?? false}
              onChange={(_, checked) => setEmptyValuesValidation(checked)}
            />
          </Row>
          <Row label='Валидация изменения ближайших значений' contentClassName={cls.closedParameters}>
            <Switch
              checked={currentSettings.allowableChangeValidation ?? false}
              onChange={(_, checked) => setAllowableChangeValidation(checked)}
            />
            <TextField
              label='метры'
              type='number'
              numberOption={{ lengthAfterComma: 3 }}
              value={
                Number.isNaN(Number(currentSettings.levelAllowableChange)) ? '' : currentSettings.levelAllowableChange
              }
              onChange={(e) => {
                setLevelAllowableChange(e.target.value)
              }}
              disabled={!currentSettings.allowableChangeValidation}
              error={currentSettings.allowableChangeValidation && !currentSettings.levelAllowableChange}
              className={cls.closedParameterFieldWidth}
            />
            <TextField
              label={unit}
              type='number'
              numberOption={{ lengthAfterComma: 3 }}
              value={
                Number.isNaN(Number(currentSettings.volumeAllowableChange)) ? '' : currentSettings.volumeAllowableChange
              }
              onChange={(e) => setVolumeAllowableChange(e.target.value)}
              disabled={!currentSettings.allowableChangeValidation}
              error={currentSettings.allowableChangeValidation && !currentSettings.volumeAllowableChange}
              className={cls.closedParameterFieldWidth}
            />
          </Row>
          <Row label='Валидация монотонности изменения значений'>
            <Switch
              checked={currentSettings.monotonyValidation ?? false}
              onChange={(_, checked) => setMonotonyValidation(checked)}
            />
          </Row>
          {currentSettings.polynom.map((constant, index) => (
            <Row
              key={index}
              label={
                index === 3 ? (
                  <div className={cls.polynomRow}>
                    <p>Полином</p>
                    <OffsettedTooltip
                      title={
                        <div>
                          C<sub>0</sub> + C<sub>1</sub> &bull; x + C<sub>2</sub> &bull; x<sup>2</sup> + C<sub>3</sub>{' '}
                          &bull; x<sup>3</sup> + C<sub>4</sub> &bull; x<sup>4</sup> + C<sub>5</sub> &bull; x<sup>5</sup>{' '}
                          + C<sub>6</sub> &bull; x<sup>6</sup>
                        </div>
                      }
                      offset={[160, -70]}
                    />
                  </div>
                ) : null
              }
            >
              <TextField
                label={`C${index}`}
                type='number'
                numberOption={{ allowExponent: true, lengthAfterComma: 20 }}
                value={constant ?? ''}
                onChange={(e) => setPolynom(index, e.target.value)}
                error={Number.isNaN(Number(constant))}
                className={cls.defaultFieldWidth}
              />
            </Row>
          ))}
          <Row label='Данные, используемые в расчётах'>
            <RadioGroup
              row
              value={currentSettings.method ?? 'TABLE'}
              onChange={(e) => setMethod(e.target.value as IReservoirVolumeSettings['method'])}
            >
              <FormControlLabel className={cls.radioButton} value='TABLE' control={<Radio />} label='Таблица' />
              <FormControlLabel className={cls.radioButton} value='POLYNOM' control={<Radio />} label='Полином' />
            </RadioGroup>
          </Row>
        </div>
      </Modal>
    </div>
  )
})
