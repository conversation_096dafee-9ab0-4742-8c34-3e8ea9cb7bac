import { makeAutoObservable, runInAction } from 'mobx'
import { getStyledComment } from 'pages/CalculationsPage/ui/StationBody/lib'
import api from 'shared/api/index'
import { klona } from 'shared/lib/klona'
import { RootStore } from 'stores/RootStore'

import { validateCharacteristicsTableData } from '../lib'
import { formatReservoirVolumesSpreadsheetData, getCellStyle } from './lib'
import { IWerReservoirVolumeStore } from './WerReservoirVolumeStore.types'

const emptySpreadsheetStructure: IWerReservoirVolumeStore['characteristicsDataSpreadsheet'] = {
  columns: [],
  data: [],
  nestedHeaders: [],
  cell: [],
  rowHeaders: [],
}

export class WerReservoirVolumeStore implements IWerReservoirVolumeStore {
  rootStore: IWerReservoirVolumeStore['rootStore']
  initialSettings: IWerReservoirVolumeStore['initialSettings'] = null
  currentSettings: IWerReservoirVolumeStore['currentSettings'] = null
  isSettingsSaving: IWerReservoirVolumeStore['isSettingsSaving'] = false
  lastFilledRow: IWerReservoirVolumeStore['lastFilledRow'] = null
  isCharacteristicsLoaded: IWerReservoirVolumeStore['isCharacteristicsLoaded'] = false
  characteristicsDataSpreadsheet: IWerReservoirVolumeStore['characteristicsDataSpreadsheet'] = {
    ...emptySpreadsheetStructure,
  }
  originalCharacteristicsDataSpreadsheet: IWerReservoirVolumeStore['originalCharacteristicsDataSpreadsheet'] = {
    ...emptySpreadsheetStructure,
  }
  isEditRows: IWerReservoirVolumeStore['isEditRows'] = false
  vbLevelIndicators: IWerReservoirVolumeStore['vbLevelIndicators'] = []
  currentVbLevelIndicator: IWerReservoirVolumeStore['currentVbLevelIndicator'] = null

  constructor(rootStore: RootStore) {
    this.rootStore = rootStore
    makeAutoObservable(this, {
      // Отключаем наблюдение, что бы ускорить инициализацию таблицы
      originalCharacteristicsDataSpreadsheet: false,
    })
  }

  resetStore: IWerReservoirVolumeStore['resetStore'] = () => {
    this.isCharacteristicsLoaded = false
    this.isEditRows = false
  }

  getReservoirVolumeCharacteristics: IWerReservoirVolumeStore['getReservoirVolumeCharacteristics'] = async (
    plantId: number,
    date: string,
  ) => {
    try {
      this.isCharacteristicsLoaded = false
      const data = await api.calcModelWerManager.getReservoirVolumeCharacteristics(plantId, date)
      const formattedSpreadsheet = formatReservoirVolumesSpreadsheetData(
        data.table,
        this.rootStore.calcModelWerStore.listOfStationsStore.characteristicsStore.editMode,
        this.rootStore.calcModelWerStore.isSelectedDateEditable,
        this.rootStore.calcModelWerStore.isSelectedPlantViewOnly,
        data.settings.levelAllowableChange,
        data.settings.volumeAllowableChange,
      )
      runInAction(() => {
        this.currentSettings = data.settings
        this.initialSettings = klona(data.settings)
        this.lastFilledRow = data.lastFilledRow
        this.characteristicsDataSpreadsheet = formattedSpreadsheet
        this.originalCharacteristicsDataSpreadsheet = formattedSpreadsheet
        this.isCharacteristicsLoaded = true
      })
    } catch (error) {
      console.error('Произошла ошибка при загрузке характеристики водохранилища', error)
    }
  }

  saveReservoirVolumeSettings: IWerReservoirVolumeStore['saveReservoirVolumeSettings'] = async (
    plantId,
    date,
    settings,
  ) => {
    try {
      const normalizedSettings = this._normalizeSettings(settings)
      if (!normalizedSettings) return

      this.isSettingsSaving = true

      await api.calcModelWerManager.saveReservoirVolumeSettings(plantId, date, normalizedSettings)

      runInAction(() => {
        this.currentSettings = normalizedSettings
        this.initialSettings = klona(normalizedSettings)
      })
      await this.getReservoirVolumeCharacteristics(plantId, date)
    } catch (error) {
      console.error('Произошла ошибка при сохранении настроек водохранилища', error)
      throw error
    } finally {
      runInAction(() => {
        this.isSettingsSaving = false
      })
    }
  }

  // Конвертация строковых значений в числовые для сравнения изменений и перед сохранением
  private readonly _normalizeSettings = (settings: IWerReservoirVolumeStore['currentSettings']) => {
    if (!settings) return null

    const keysToConvert = ['rowCount', 'levelAllowableChange', 'volumeAllowableChange'] as const
    const normalizedSettings = { ...settings }

    keysToConvert.forEach((key) => {
      if (typeof normalizedSettings[key] === 'string') {
        if (key !== 'rowCount') {
          normalizedSettings[key] = normalizedSettings[key] === '' ? undefined : Number(normalizedSettings[key])
        } else {
          normalizedSettings[key] = Number(normalizedSettings[key])
        }
      }
    })

    if (Array.isArray(normalizedSettings.polynom)) {
      normalizedSettings.polynom = normalizedSettings.polynom.map((value) => {
        if (typeof value === 'string') {
          return value === '' ? null : Number(value)
        }

        return value
      })
    }

    return normalizedSettings
  }

  updateCharacteristicsSpreadsheetData: IWerReservoirVolumeStore['updateCharacteristicsSpreadsheetData'] = (
    changes,
  ) => {
    if (!changes) return

    // После каждого изменения в таблице валидируем ее
    const errorsMap = validateCharacteristicsTableData(this.characteristicsDataSpreadsheet.data, {
      0: this.initialSettings?.levelAllowableChange,
      1: this.initialSettings?.volumeAllowableChange,
    })

    // Состояния, от которых зависит, доступна ли таблица к редактированию
    const canEdit = this.rootStore.calcModelWerStore.listOfStationsStore.characteristicsStore.editMode
    const isSelectedDateEditable = this.rootStore.calcModelWerStore.isSelectedDateEditable
    const isSelectedPlantViewOnly = this.rootStore.calcModelWerStore.isSelectedPlantViewOnly

    // Обновляем массив cell: для каждой ячейки заново вычисляем className,
    // а также добавляем комментарий, если найдена ошибка
    const cells = this.characteristicsDataSpreadsheet.cell
    cells.forEach((cell) => {
      const cellKey = `${cell.row}:${cell.col}`
      const message = errorsMap.get(cellKey)
      // Оптимизация. Если сообщение не изменилось, то не меняем className, comment
      if (cell.message !== message) {
        cell.className = getCellStyle({
          cell,
          canEdit,
          isSelectedDateEditable,
          isSelectedPlantViewOnly,
          message,
        })
        cell.comment = message ? getStyledComment(message) : undefined
        cell.message = message // Сохраняем для сравнения
      }
    })
    // Пересоздаем массив, чтобы изменить ссылку, чтобы MobX обнаружил изменения
    this.characteristicsDataSpreadsheet.cell = cells.slice()
    this.isEditRows = true
  }

  saveChangedCharacteristicsSpreadsheetData: IWerReservoirVolumeStore['saveChangedCharacteristicsSpreadsheetData'] =
    async () => {
      const plantId = this.rootStore.calcModelWerStore.selectedPlant?.plantId
      const date = this.rootStore.calcModelWerStore.formattedDate

      if (!plantId) {
        console.log('Отсутствует ID станции')

        return
      }

      try {
        this.isSettingsSaving = true

        const changesPayload = {
          matrix: this.characteristicsDataSpreadsheet.data,
        }
        await api.calcModelWerManager.saveReservoirVolumeCharacteristics(plantId, date, changesPayload)

        this.rootStore.notificationStore.addNotification({
          title: 'Сохранение РМ',
          description: 'Изменения сохранены',
          type: 'success',
        })

        await this.getReservoirVolumeCharacteristics(plantId, date)
        runInAction(() => {
          this.isEditRows = false
        })
      } catch (error) {
        console.error('Ошибка при сохранении изменений характеристик водохранилища', error)
      } finally {
        runInAction(() => {
          this.isSettingsSaving = false
        })
      }
    }

  resetCharacteristicsSpreadsheetData: IWerReservoirVolumeStore['resetCharacteristicsSpreadsheetData'] = () => {
    runInAction(() => {
      this.characteristicsDataSpreadsheet = klona(this.originalCharacteristicsDataSpreadsheet)
      this.isEditRows = false
    })
  }

  resetSettings: IWerReservoirVolumeStore['resetSettings'] = () => {
    this.currentSettings = klona(this.initialSettings)
  }

  get isSettingsModified(): IWerReservoirVolumeStore['isSettingsModified'] {
    const normalizedCurrent = this._normalizeSettings(this.currentSettings)

    return JSON.stringify(normalizedCurrent) !== JSON.stringify(this.initialSettings)
  }

  setRowCount: IWerReservoirVolumeStore['setRowCount'] = (rowCount) => {
    if (this.currentSettings && !Number.isNaN(rowCount)) {
      this.currentSettings.rowCount = rowCount
    }
  }

  setUnit: IWerReservoirVolumeStore['setUnit'] = (unit) => {
    if (this.currentSettings) {
      this.currentSettings.unit = unit
    }
  }

  setLevelAllowableChange: IWerReservoirVolumeStore['setLevelAllowableChange'] = (levelAllowableChange) => {
    if (this.currentSettings && !Number.isNaN(levelAllowableChange)) {
      this.currentSettings.levelAllowableChange = levelAllowableChange
    }
  }

  setVolumeAllowableChange: IWerReservoirVolumeStore['setVolumeAllowableChange'] = (volumeAllowableChange) => {
    if (this.currentSettings && !Number.isNaN(volumeAllowableChange)) {
      this.currentSettings.volumeAllowableChange = volumeAllowableChange
    }
  }

  setEmptyValuesValidation: IWerReservoirVolumeStore['setEmptyValuesValidation'] = (emptyValuesValidation) => {
    if (this.currentSettings) {
      this.currentSettings.emptyValuesValidation = emptyValuesValidation
    }
  }

  setAllowableChangeValidation: IWerReservoirVolumeStore['setAllowableChangeValidation'] = (
    allowableChangeValidation,
  ) => {
    if (this.currentSettings) {
      this.currentSettings.allowableChangeValidation = allowableChangeValidation
    }
  }

  setMonotonyValidation: IWerReservoirVolumeStore['setMonotonyValidation'] = (monotonyValidation) => {
    if (this.currentSettings) {
      this.currentSettings.monotonyValidation = monotonyValidation
    }
  }

  setPolynom: IWerReservoirVolumeStore['setPolynom'] = (index, value) => {
    if (this.currentSettings?.polynom && !Number.isNaN(value)) {
      this.currentSettings.polynom[index] = value
    }
  }

  setMethod: IWerReservoirVolumeStore['setMethod'] = (method) => {
    if (this.currentSettings) {
      this.currentSettings.method = method
    }
  }

  getReservoirVolumeIndicatorVbLevel: IWerReservoirVolumeStore['getReservoirVolumeIndicatorVbLevel'] = async (
    plantId,
  ) => {
    try {
      const res = await api.calcModelWerManager.getReservoirVolumeIndicatorVbLevel({ plantId })
      runInAction(() => {
        this.vbLevelIndicators = res.indicators.map((el) => ({ label: el.displayName, value: el.id.toString() }))
        this.currentVbLevelIndicator = res.selectedIndicator
      })
    } catch (error) {
      console.error(
        'Произошла ошибка при получении текущего и всех доступных индикаторов уровня ВБ на вкладке характеристики водохранилища',
        error,
      )
    }
  }

  updateCurrentVbLevelIndicator: IWerReservoirVolumeStore['updateCurrentVbLevelIndicator'] = async (id, plantId) => {
    try {
      await api.calcModelWerManager.updateReservoirVolumeIndicatorVbLevel({
        plantId,
        indicatorId: Number(id),
      })
      const currentVbLevelIndicator = this.vbLevelIndicators.find((el) => el.value === id)
      this.currentVbLevelIndicator = currentVbLevelIndicator
        ? { displayName: currentVbLevelIndicator.label as string, id: Number(currentVbLevelIndicator.value) }
        : null
    } catch (error) {
      console.error(
        'Произошла ошибка при обновлении текущего индикатора уровня ВБ на вкладке характеристики водохранилища',
        error,
      )
    }
  }
}
