import { AxiosError } from 'axios'
import { format } from 'date-fns'
import { CharacteristicsSpreadsheetChanges } from 'entities/api/calcModelWerManager.entities'
import { makeAutoObservable, runInAction } from 'mobx'
import { getStyledComment } from 'pages/CalculationsPage/ui/StationBody/lib'
import api from 'shared/api/index'
import { klona } from 'shared/lib/klona'
import { RootStore } from 'stores/RootStore'

import { formatTailraceSpreadsheetData, getCellStyle, validateTailraceCharacteristicsTableData } from './lib'
import { IWerTailraceStore } from './WerTailraceStore.types'

const emptySpreadsheetStructure: IWerTailraceStore['tailraceSpreadsheetData'] = {
  columns: [],
  data: [],
  nestedHeaders: [],
  cell: [],
  rowHeaders: [],
}

export class WerTailraceStore implements IWerTailraceStore {
  rootStore: IWerTailraceStore['rootStore']
  initialSettings: IWerTailraceStore['initialSettings'] = null
  currentSettings: IWerTailraceStore['currentSettings'] = null
  isSettingsSaving: IWerTailraceStore['isSettingsSaving'] = false
  isCharacteristicsLoaded: IWerTailraceStore['isCharacteristicsLoaded'] = false
  isCharacteristicsSaving: IWerTailraceStore['isCharacteristicsSaving'] = false
  downstreamPlantList: IWerTailraceStore['downstreamPlantList'] = []
  selectedDownstreamPlant: IWerTailraceStore['selectedDownstreamPlant'] = undefined
  lastFilledRow: IWerTailraceStore['lastFilledRow'] = null
  tailraceSpreadsheetData: IWerTailraceStore['tailraceSpreadsheetData'] = {
    ...emptySpreadsheetStructure,
  }
  originalTailraceSpreadsheetData: IWerTailraceStore['tailraceSpreadsheetData'] = {
    ...emptySpreadsheetStructure,
  }
  isEditRows: IWerTailraceStore['isEditRows'] = false
  isFreezing: IWerTailraceStore['isFreezing'] = false
  freezingLastFilledRow: IWerTailraceStore['freezingLastFilledRow'] = null
  freezingTailraceSpreadsheetData: IWerTailraceStore['tailraceSpreadsheetData'] = {
    ...emptySpreadsheetStructure,
  }
  originalFreezingTailraceSpreadsheetData: IWerTailraceStore['tailraceSpreadsheetData'] = {
    ...emptySpreadsheetStructure,
  }
  actualData: IWerTailraceStore['actualData'] = []
  isActualDataLoading: IWerTailraceStore['isActualDataLoading'] = false

  constructor(rootStore: RootStore) {
    this.rootStore = rootStore
    makeAutoObservable(this, {
      // Отключаем наблюдение, что бы ускорить инициализацию таблицы
      originalTailraceSpreadsheetData: false,
      originalFreezingTailraceSpreadsheetData: false,
    })
  }

  resetStore: IWerTailraceStore['resetStore'] = () => {
    this.isCharacteristicsLoaded = false
    this.isEditRows = false
    this.isFreezing = false
    this.actualData = []
  }

  _getDownstreamPlantList: IWerTailraceStore['_getDownstreamPlantList'] = async (plantId, date) => {
    try {
      const data = await api.calcModelWerManager.getDownstreamPlantList(plantId, date)
      runInAction(() => {
        this.downstreamPlantList = data
      })
      // Если список не пустой — выбираем первую станцию по умолчанию
      if (data && data.length > 0) {
        this.setSelectedDownstreamPlant(data[0])
      } else {
        this.setSelectedDownstreamPlant(undefined)
      }
    } catch (error) {
      console.error(
        'Произошла ошибка при загрузке списка нижележащих станций с включенной характеристикой "Подпор"',
        error,
      )
    }
  }

  setSelectedDownstreamPlant: IWerTailraceStore['setSelectedDownstreamPlant'] = (downstreamPlant) => {
    this.selectedDownstreamPlant = downstreamPlant
    if (this.currentSettings && downstreamPlant) {
      this.currentSettings.downstreamPlant = downstreamPlant
    }
  }

  setDownstreamPlantLevel: IWerTailraceStore['setDownstreamPlantLevel'] = (index, value) => {
    if (this.currentSettings?.downstreamPlantLevels) {
      this.currentSettings.downstreamPlantLevels[index] = value
    }
  }

  addDownstreamPlantLevel: IWerTailraceStore['addDownstreamPlantLevel'] = () => {
    if (this.currentSettings?.downstreamPlantLevels) {
      this.currentSettings.downstreamPlantLevels.push('')
    }
  }

  deleteDownstreamPlantLevel: IWerTailraceStore['deleteDownstreamPlantLevel'] = (index) => {
    if (this.currentSettings?.downstreamPlantLevels) {
      this.currentSettings.downstreamPlantLevels.splice(index, 1)
    }
  }

  getTailraceCharacteristics: IWerTailraceStore['getTailraceCharacteristics'] = async (
    plantId,
    date,
    downstreamPlant?,
  ) => {
    try {
      this.isCharacteristicsLoaded = false
      let currentDownstreamPlant = downstreamPlant

      // Если объект не передан – грузим список и выбираем первую станцию
      if (!currentDownstreamPlant) {
        await this._getDownstreamPlantList(plantId, date)
        if (this.downstreamPlantList.length > 0) {
          currentDownstreamPlant = this.downstreamPlantList[0]
          this.setSelectedDownstreamPlant(currentDownstreamPlant)
        } else {
          this.setSelectedDownstreamPlant(undefined)
        }
      } else {
        // Если передан, обновляем выбранное значение
        this.setSelectedDownstreamPlant(currentDownstreamPlant)
      }

      // Передаём на бек только plantId выбранной нижележащей станции (если объект есть)
      const currentDownstreamPlantId = currentDownstreamPlant?.plantId
      const data = await api.calcModelWerManager.getTailraceCharacteristics(plantId, date, currentDownstreamPlantId)
      const formattedSpreadsheet = formatTailraceSpreadsheetData(
        data.table,
        this.rootStore.calcModelWerStore.listOfStationsStore.characteristicsStore.editMode,
        this.rootStore.calcModelWerStore.isSelectedDateEditable,
        this.rootStore.calcModelWerStore.isSelectedPlantViewOnly,
        data.settings.consumptionAllowableChange,
        data.settings.tailraceAllowableChange,
        {
          emptyValuesValidation: data.settings.emptyValuesValidation,
          allowableChangeValidation: data.settings.allowableChangeValidation,
          monotonyValidation: data.settings.monotonyValidation,
        },
      )

      runInAction(() => {
        this.currentSettings = data.settings
        // Убедимся, что downstreamPlant соответствует selectedDownstreamPlant
        if (this.selectedDownstreamPlant && this.currentSettings) {
          this.currentSettings.downstreamPlant = this.selectedDownstreamPlant

          // Инициализируем массив уровней, если он еще не существует
          if (!this.currentSettings.downstreamPlantLevels) {
            this.currentSettings.downstreamPlantLevels = []
          }
        }
        this.initialSettings = klona(data.settings)
        this.lastFilledRow = data.lastFilledRow
        this.tailraceSpreadsheetData = formattedSpreadsheet
        this.originalTailraceSpreadsheetData = formattedSpreadsheet

        // Добавляем обработку данных для зимней таблицы при включенной хар-ке "Учет ледостава в НБ"
        // и при наличии нижележащих ГЭС с включенным подпором
        if (data.freezingTable && this.initialSettings?.freezingWatch) {
          const formattedFreezingSpreadsheet = formatTailraceSpreadsheetData(
            data.freezingTable,
            this.rootStore.calcModelWerStore.listOfStationsStore.characteristicsStore.editMode,
            this.rootStore.calcModelWerStore.isSelectedDateEditable,
            this.rootStore.calcModelWerStore.isSelectedPlantViewOnly,
            data.settings.consumptionAllowableChange,
            data.settings.tailraceAllowableChange,
            {
              emptyValuesValidation: data.settings.emptyValuesValidation ?? false,
              allowableChangeValidation: data.settings.allowableChangeValidation ?? false,
              monotonyValidation: data.settings.monotonyValidation ?? false,
            },
          )

          this.freezingLastFilledRow = data.freezingLastFilledRow
          this.freezingTailraceSpreadsheetData = formattedFreezingSpreadsheet
          this.originalFreezingTailraceSpreadsheetData = formattedFreezingSpreadsheet
        }

        this.isCharacteristicsLoaded = true
      })
    } catch (error) {
      console.error('Произошла ошибка при загрузке характеристики НБ', error)
    }
  }

  saveTailraceSettings: IWerTailraceStore['saveTailraceSettings'] = async (plantId, date, settings) => {
    try {
      const normalizedSettings = this._normalizeSettings(settings)
      if (!normalizedSettings) return

      this.isSettingsSaving = true

      await api.calcModelWerManager.saveTailraceSettings(plantId, date, normalizedSettings)

      runInAction(() => {
        this.currentSettings = normalizedSettings
        this.initialSettings = klona(normalizedSettings)
      })
      await this.getTailraceCharacteristics(plantId, date, this.selectedDownstreamPlant)
    } catch (error) {
      console.error('Произошла ошибка при сохранении настроек НБ', error)
      throw error
    } finally {
      runInAction(() => {
        this.isSettingsSaving = false
      })
    }
  }

  // Конвертация данных для сравнения изменений и перед отправкой на сервер для сохранения
  private readonly _normalizeSettings = (settings: IWerTailraceStore['currentSettings']) => {
    if (!settings) return null

    const keysToConvert = ['rowCount', 'tailraceAllowableChange', 'consumptionAllowableChange'] as const
    const normalizedSettings = { ...settings }

    // Обработка значений полей: количества строк, уровня нижнего бьефа и расхода воды
    keysToConvert.forEach((key) => {
      if (typeof normalizedSettings[key] === 'string') {
        if (key !== 'rowCount') {
          normalizedSettings[key] = normalizedSettings[key] === '' ? undefined : Number(normalizedSettings[key])
        } else {
          normalizedSettings[key] = Number(normalizedSettings[key])
        }
      }
    })

    // Обработка коэффициентов полинома
    if (Array.isArray(normalizedSettings.polynom)) {
      normalizedSettings.polynom = normalizedSettings.polynom.map((value) => {
        if (typeof value === 'string') {
          return value === '' ? null : Number(value)
        }

        return value
      })
    }

    // Обработка уровней водохранилища выбранной нижележащей станции
    if (Array.isArray(normalizedSettings.downstreamPlantLevels)) {
      normalizedSettings.downstreamPlantLevels = normalizedSettings.downstreamPlantLevels.map((value) => {
        return typeof value === 'string' ? Number(value) : value
      })
    }

    return normalizedSettings
  }

  updateCharacteristicsSpreadsheetData: IWerTailraceStore['updateCharacteristicsSpreadsheetData'] = (changes) => {
    if (!changes) return

    // Выбираем данные в зависимости от типа таблицы
    const tableData = this.freezing ? this.freezingTailraceSpreadsheetData : this.tailraceSpreadsheetData

    // После каждого изменения в таблице валидируем ее
    const errorsMap = validateTailraceCharacteristicsTableData(
      tableData.data,
      {
        consumptionAllowableChange: this.initialSettings?.consumptionAllowableChange,
        tailraceAllowableChange: this.initialSettings?.tailraceAllowableChange,
      },
      {
        emptyValuesValidation: this.initialSettings?.emptyValuesValidation,
        allowableChangeValidation: this.initialSettings?.allowableChangeValidation,
        monotonyValidation: this.initialSettings?.monotonyValidation,
      },
    )

    // Состояния, от которых зависит, доступна ли таблица к редактированию
    const canEdit = this.rootStore.calcModelWerStore.listOfStationsStore.characteristicsStore.editMode
    const isSelectedDateEditable = this.rootStore.calcModelWerStore.isSelectedDateEditable
    const isSelectedPlantViewOnly = this.rootStore.calcModelWerStore.isSelectedPlantViewOnly

    // Обновляем массив cell: для каждой ячейки заново вычисляем className,
    // а также добавляем комментарий, если найдена ошибка
    const cells = tableData.cell
    cells.forEach((cell) => {
      const cellKey = `${cell.row}:${cell.col}`
      const message = errorsMap.get(cellKey)
      // Оптимизация. Если сообщение не изменилось, то не меняем className, comment
      if (cell.message !== message) {
        cell.className = getCellStyle({
          cell,
          canEdit,
          isSelectedDateEditable,
          isSelectedPlantViewOnly,
          message,
        })
        cell.comment = message ? getStyledComment(message) : undefined
        cell.message = message // Сохраняем для сравнения
      }
    })
    // Пересоздаём массив, чтобы изменить ссылку и MobX обнаружил изменения
    if (this.freezing) {
      this.freezingTailraceSpreadsheetData.cell = cells.slice()
    } else {
      this.tailraceSpreadsheetData.cell = cells.slice()
    }

    this.isEditRows = true
  }

  saveChangedCharacteristicsSpreadsheetData: IWerTailraceStore['saveChangedCharacteristicsSpreadsheetData'] =
    async () => {
      const plantId = this.rootStore.calcModelWerStore.selectedPlant?.plantId
      const date = this.rootStore.calcModelWerStore.formattedDate
      const downstreamPlantId = this.selectedDownstreamPlant?.plantId

      if (!plantId) {
        console.log('Отсутствует ID станции')

        return
      }

      try {
        this.isCharacteristicsSaving = true

        // Формируем объект для отправки
        const freezing = this.freezing
        const matrix = freezing ? this.freezingTailraceSpreadsheetData.data : this.tailraceSpreadsheetData.data

        const payload: CharacteristicsSpreadsheetChanges = { matrix }

        // Добавляем необходимые параметры в зависимости от условий
        if (downstreamPlantId) {
          payload.downstreamPlantId = downstreamPlantId
        }

        if (freezing !== undefined) {
          payload.freezing = freezing
        }

        await api.calcModelWerManager.saveTailraceCharacteristics(plantId, date, payload)

        this.rootStore.notificationStore.addNotification({
          title: 'Сохранение РМ',
          description: 'Изменения сохранены',
          type: 'success',
        })

        runInAction(() => {
          this.isEditRows = false
        })
        await this.getTailraceCharacteristics(plantId, date, this.selectedDownstreamPlant)
      } catch (error) {
        console.error('Ошибка при сохранении изменений характеристик НБ', error)
      } finally {
        runInAction(() => {
          this.isCharacteristicsSaving = false
        })
      }
    }

  resetCharacteristicsSpreadsheetData: IWerTailraceStore['resetCharacteristicsSpreadsheetData'] = () => {
    runInAction(() => {
      if (this.freezing) {
        this.freezingTailraceSpreadsheetData = klona(this.originalFreezingTailraceSpreadsheetData)
      } else {
        this.tailraceSpreadsheetData = klona(this.originalTailraceSpreadsheetData)
      }
      this.isEditRows = false
    })
  }

  // Флаг, определяющий вид таблицы: без нижележащих станций с включенным подпором,
  // с выключенным "Учетом ледостава НБ", зимняя или летняя
  get freezing(): IWerTailraceStore['freezing'] {
    const hasFreezingWatch = !!this.initialSettings?.freezingWatch
    const hasDownstream = this.downstreamPlantList.length > 0

    if (!hasFreezingWatch || !hasDownstream) {
      return undefined
    }

    return this.isFreezing
  }

  toggleFreezing: IWerTailraceStore['toggleFreezing'] = () => {
    this.isFreezing = !this.isFreezing
  }

  resetSettings: IWerTailraceStore['resetSettings'] = () => {
    const currentDownstreamPlant = this.currentSettings?.downstreamPlant
    this.currentSettings = klona(this.initialSettings)

    // Сохраняем downstreamPlant и инициализируем downstreamPlantLevels если нужно
    if (this.currentSettings && currentDownstreamPlant) {
      this.currentSettings.downstreamPlant = currentDownstreamPlant
      if (!this.currentSettings.downstreamPlantLevels) {
        this.currentSettings.downstreamPlantLevels = []
      }
    }
  }

  getActualData: IWerTailraceStore['getActualData'] = async (plantId, date, startDate, endDate, signal) => {
    try {
      this.isActualDataLoading = true
      const data = await api.calcModelWerManager.getTailraceActualMeasurements(
        plantId,
        date,
        startDate,
        endDate,
        signal,
      )
      runInAction(() => {
        this.actualData = data
      })

      this.rootStore.notificationStore.addNotification({
        title: 'Загрузка фактических данных из ИСС ГЭС',
        description: 'Загрузка выполнена',
        type: 'success',
      })
    } catch (e) {
      const error = e as AxiosError
      if (error.code !== 'ERR_CANCELED') {
        console.error('Ошибка загрузки фактических данных', error)
        // Пробрасываем ошибку для обработки в компоненте
        throw error
      }
    } finally {
      runInAction(() => {
        this.isActualDataLoading = false
      })
    }
  }

  get seriesActualData(): IWerTailraceStore['seriesActualData'] {
    return this.actualData.map(([x, y]) => ({
      x: Number(x),
      y: Number(y),
    }))
  }

  get isSettingsModified(): IWerTailraceStore['isSettingsModified'] {
    const normalizedCurrent = this._normalizeSettings(this.currentSettings)

    return JSON.stringify(normalizedCurrent) !== JSON.stringify(this.initialSettings)
  }

  setRowCount: IWerTailraceStore['setRowCount'] = (rowCount) => {
    if (this.currentSettings && !Number.isNaN(rowCount)) {
      this.currentSettings.rowCount = rowCount
    }
  }

  setFreezingWatch: IWerTailraceStore['setFreezingWatch'] = (isFreezingWatch) => {
    if (this.currentSettings) {
      this.currentSettings.freezingWatch = isFreezingWatch
    }
  }

  setFreezingDate: IWerTailraceStore['setFreezingDate'] = (dateType, date) => {
    if (this.currentSettings?.freezingWatch) {
      const formattedDate = format(date, "'--'MM-dd")
      this.currentSettings[dateType] = formattedDate
    }
  }

  setTailraceAllowableChange: IWerTailraceStore['setTailraceAllowableChange'] = (tailraceAllowableChange) => {
    if (this.currentSettings && !Number.isNaN(tailraceAllowableChange)) {
      this.currentSettings.tailraceAllowableChange = tailraceAllowableChange
    }
  }

  setConsumptionAllowableChange: IWerTailraceStore['setConsumptionAllowableChange'] = (consumptionAllowableChange) => {
    if (this.currentSettings && !Number.isNaN(consumptionAllowableChange)) {
      this.currentSettings.consumptionAllowableChange = consumptionAllowableChange
    }
  }

  setEmptyValuesValidation: IWerTailraceStore['setEmptyValuesValidation'] = (emptyValuesValidation) => {
    if (this.currentSettings) {
      this.currentSettings.emptyValuesValidation = emptyValuesValidation
    }
  }

  setAllowableChangeValidation: IWerTailraceStore['setAllowableChangeValidation'] = (allowableChangeValidation) => {
    if (this.currentSettings) {
      this.currentSettings.allowableChangeValidation = allowableChangeValidation
    }
  }

  setMonotonyValidation: IWerTailraceStore['setMonotonyValidation'] = (monotonyValidation) => {
    if (this.currentSettings) {
      this.currentSettings.monotonyValidation = monotonyValidation
    }
  }

  setPolynom: IWerTailraceStore['setPolynom'] = (index, value) => {
    if (this.currentSettings?.polynom && !Number.isNaN(value)) {
      this.currentSettings.polynom[index] = value
    }
  }

  setMethod: IWerTailraceStore['setMethod'] = (method) => {
    if (this.currentSettings) {
      this.currentSettings.method = method
    }
  }
}
