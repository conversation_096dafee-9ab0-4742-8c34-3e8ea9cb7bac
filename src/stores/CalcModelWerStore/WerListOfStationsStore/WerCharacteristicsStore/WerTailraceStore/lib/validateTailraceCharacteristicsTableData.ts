import { ITailraceTableWithSettingsResponse } from 'entities/api/calcModelWerManager.entities'

/**
 * Тип, описывающий значение ячейки.
 */
type DataValue = string | number | undefined

/**
 * Тип, описывающий допустимые изменения значений для столбцов
 * для валидации по правилу 2.
 *
 * consumptionAllowableChange - допустимое изменение для первого столбца (индекс 0)
 * tailraceAllowableChange - допустимое изменение для всех остальных столбцов
 */
type AllowableChanges = {
  consumptionAllowableChange: ITailraceTableWithSettingsResponse['settings']['consumptionAllowableChange']
  tailraceAllowableChange: ITailraceTableWithSettingsResponse['settings']['tailraceAllowableChange']
}

/**
 * Преобразование всей матрицы данных за один проход.
 * Каждая ячейка, если пуста ('', null или undefined),
 * преобразуется в null, иначе – в число.
 */
const getParsedMatrix = (data: DataValue[][]): (number | null)[][] => {
  return data.map((row) =>
    row.map((cell) => {
      if (cell === '' || cell === null || cell === undefined) return null

      return Number(cell)
    }),
  )
}

/**
 * Функция для добавления сообщения об ошибке.
 *
 * @param errorMap - Map с ключом в формате "row:col" для хранения ошибок.
 * @param row - Индекс строки.
 * @param col - Индекс столбца.
 * @param message - Сообщение об ошибке.
 */
const addError = (errorMap: Map<string, Set<string>>, row: number, col: number, message: string): void => {
  const key = `${row}:${col}`
  const existingErrors = errorMap.get(key)
  if (existingErrors) {
    existingErrors.add(message)
  } else {
    errorMap.set(key, new Set([message]))
  }
}

/**
 * Находит индексы последних заполненных ячеек для каждого столбца
 */
const findLastFilledRowIndices = (parsedData: (number | null)[][], rowCount: number, colCount: number): number[] => {
  const lastFilledIndices: number[] = []

  for (let col = 0; col < colCount; col++) {
    let lastFilled = -1
    for (let row = rowCount - 1; row >= 0; row--) {
      if (parsedData[row][col] !== null) {
        lastFilled = row
        break
      }
    }
    lastFilledIndices[col] = lastFilled
  }

  return lastFilledIndices
}

/**
 * Правило 1.
 *
 * Если ячейка пустая, а предыдущая и следующая заполнены, выдаем ошибку.
 * Это правило проверяется для всех "средних" ячеек (т.е. если есть и предыдущая, и следующая).
 */
const validateRule1 = (
  parsedData: (number | null)[][],
  colCount: number,
  lastFilledIndices: number[],
  errorMap: Map<string, Set<string>>,
): void => {
  for (let col = 0; col < colCount; col++) {
    const lastFilled = lastFilledIndices[col]

    // Если нашли последнюю заполненную, проверяем пропуски до нее
    if (lastFilled > 0) {
      for (let row = 0; row < lastFilled; row++) {
        if (parsedData[row][col] === null) {
          addError(errorMap, row, col, 'Значение не должно быть пустым')
        }
      }
    }
  }
}

/**
 * Правило 2.
 *
 * Абсолютная разница между двумя соседними значениями
 * в одном столбце должна быть не больше limit и не равна нулю.
 *
 * limit - это величина, которая определяется в настройках характеристики
 * и зависит от индекса столбца (allowableChanges[col])
 */
const validateRule2 = (
  parsedData: (number | null)[][],
  colCount: number,
  lastFilledIndices: number[],
  errorMap: Map<string, Set<string>>,
  consumptionAllowableChange: ITailraceTableWithSettingsResponse['settings']['consumptionAllowableChange'],
  tailraceAllowableChange: ITailraceTableWithSettingsResponse['settings']['tailraceAllowableChange'],
): void => {
  for (let col = 0; col < colCount; col++) {
    // Выбираем лимит в зависимости от колонки
    const rawLimit = col === 0 ? consumptionAllowableChange : tailraceAllowableChange
    const limit = rawLimit !== undefined ? Number(rawLimit) : undefined
    const lastFilled = lastFilledIndices[col]

    // Проходим по парам соседних ячеек в столбце до последней заполненной
    for (let row = 1; row <= lastFilled; row++) {
      const prevNum = parsedData[row - 1][col]
      const currNum = parsedData[row][col]
      // Если оба значения определены (не null), то проверяем разницу.
      if (prevNum !== null && currNum !== null) {
        const diff = Math.abs(currNum - prevNum)
        // Округляем разницу до трех знаков после запятой, чтобы нивелировать
        // мелкие погрешности вычислений с плавающей точкой. Без этого округления
        // арифметические неточности могут приводить к ложным срабатываниям валидации.
        const roundedDiff = Math.round(diff * 1000) / 1000
        if (roundedDiff === 0) {
          // Всегда проверяем на равенство нулю
          addError(errorMap, row, col, `Разница между значениями должна быть > 0`)
        } else if (limit !== undefined && roundedDiff > limit) {
          // Проверяем на превышение лимита только если лимит определён
          addError(errorMap, row, col, `Разница между значениями должна быть > 0 и не превышать ${limit}`)
        }
      }
    }
  }
}

/**
 * Правило 3.
 *
 * Значения в обоих столбцах должны либо одновременно строго возрастать,
 * либо строго убывать.
 *
 * Логика проверки для набора из трёх последовательных строк (i-1, i, i+1):
 *
 * 1. Определяем направление изменений для первой пары:
 *    - trendFirst_0 = sign(parsedData[i][0] - parsedData[i-1][0])
 *    - trendFirst_1 = sign(parsedData[i][1] - parsedData[i-1][1])
 *
 *    Если направления в столбцах различаются, то ошибка ставится в строке i.
 *
 * 2. Если первая пара согласована (общий тренд),
 *    сравниваем его со знаком разницы для второй пары:
 *    - trendSecond_0 = sign(parsedData[i+1][0] - parsedData[i][0])
 *    - trendSecond_1 = sign(parsedData[i+1][1] - parsedData[i][1])
 *
 *    Если хотя бы для одного столбца знак отличается от установленного
 *    общего тренда, значит, нарушение произошло в строке i+1.
 */
const validateRule3 = (
  parsedData: (number | null)[][],
  colCount: number,
  lastFilledIndices: number[],
  errorMap: Map<string, Set<string>>,
): void => {
  if (colCount <= 1) return

  // Для каждой колонки, начиная с 1-й, сравниваем тренд с колонкой 0
  for (let col = 1; col < colCount; col++) {
    // Находим минимальную из последних заполненных ячеек для колонок 0 и текущей
    const minLastFilled = Math.min(lastFilledIndices[0], lastFilledIndices[col])

    // Нет смысла проверять, если нет достаточного количества строк
    if (minLastFilled < 2) continue

    for (let row = 1; row < minLastFilled; row++) {
      const x0Prev = parsedData[row - 1][0]
      const x0Curr = parsedData[row][0]
      const x0Next = parsedData[row + 1][0]

      const yColPrev = parsedData[row - 1][col]
      const yColCurr = parsedData[row][col]
      const yColNext = parsedData[row + 1][col]

      // Если все три значения по обоим столбцам заданы:
      if (
        x0Prev !== null &&
        x0Curr !== null &&
        x0Next !== null &&
        yColPrev !== null &&
        yColCurr !== null &&
        yColNext !== null
      ) {
        // Направление изменений для первой пары
        const trendX0First = Math.sign(x0Curr - x0Prev)
        const trendYColFirst = Math.sign(yColCurr - yColPrev)

        // Если тренды для первой пары различаются
        if (trendX0First !== trendYColFirst) {
          addError(errorMap, row, 0, 'Значения должны монотонно возрастать или убывать')
          addError(errorMap, row, col, 'Значения должны монотонно возрастать или убывать')
        } else {
          // Первая пара согласована, зафиксируем общий тренд
          const commonTrend = trendX0First

          // Направление изменений для второй пары
          const trendX0Second = Math.sign(x0Next - x0Curr)
          const trendYColSecond = Math.sign(yColNext - yColCurr)

          // Если хотя бы для одного столбца тренд во второй паре отличается от общего тренда
          if (trendX0Second !== commonTrend || trendYColSecond !== commonTrend) {
            addError(errorMap, row + 1, 0, 'Значения должны монотонно возрастать или убывать')
            addError(errorMap, row + 1, col, 'Значения должны монотонно возрастать или убывать')
          }
        }
      }
    }
  }
}

// Преобразуем errorMap в Map<string, string>, объединяя уникальные сообщения через '\n'
const processErrorMap = (errorMap: Map<string, Set<string>>): Map<string, string> => {
  const processed = new Map<string, string>()
  errorMap.forEach((messages, key) => {
    processed.set(key, Array.from(messages).join('\n'))
  })

  return processed
}

/**
 * Настройки валидации для включения/отключения правил
 */
type ValidationSettings = {
  emptyValuesValidation?: boolean
  allowableChangeValidation?: boolean
  monotonyValidation?: boolean
}

/**
 * Функция для валидации всей матрицы данных таблицы.
 *
 * Параметры:
 * - data: матрица значений
 * - allowableChanges: объект с допустимыми изменениями для столбцов:
 *   - consumptionAllowableChange: допустимое изменение для первого столбца (индекс 0)
 *   - tailraceAllowableChange: допустимое изменение для всех остальных столбцов
 * - validationSettings: настройки включения/отключения правил валидации
 *
 * В этой версии мы сначала преобразуем всю матрицу за один проход,
 * чтобы уменьшить количество повторных преобразований значений ячеек.
 */
export const validateTailraceCharacteristicsTableData = (
  data: DataValue[][],
  allowableChanges: AllowableChanges,
  validationSettings: ValidationSettings = {},
): Map<string, string> => {
  // Map с ключом `row:col` и значением типа Set, который хранит уникальные сообщения
  const errorMap = new Map<string, Set<string>>()
  const rowCount = data.length
  const colCount = data[0]?.length ?? 0

  const parsedData = getParsedMatrix(data)
  const lastFilledIndices = findLastFilledRowIndices(parsedData, rowCount, colCount)

  // Применяем правила валидации только если они включены
  if (validationSettings.emptyValuesValidation) {
    validateRule1(parsedData, colCount, lastFilledIndices, errorMap)
  }

  if (validationSettings.allowableChangeValidation) {
    validateRule2(
      parsedData,
      colCount,
      lastFilledIndices,
      errorMap,
      allowableChanges.consumptionAllowableChange,
      allowableChanges.tailraceAllowableChange,
    )
  }

  if (validationSettings.monotonyValidation) {
    validateRule3(parsedData, colCount, lastFilledIndices, errorMap)
  }

  return processErrorMap(errorMap)
}
