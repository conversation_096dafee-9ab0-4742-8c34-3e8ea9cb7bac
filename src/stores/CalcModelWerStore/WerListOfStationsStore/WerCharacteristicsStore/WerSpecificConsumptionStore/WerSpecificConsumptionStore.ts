import { AxiosError } from 'axios'
import { makeAutoObservable, runInAction } from 'mobx'
import { getStyledComment } from 'pages/CalculationsPage/ui/StationBody/lib'
import api from 'shared/api/index'
import { klona } from 'shared/lib/klona'
import { RootStore } from 'stores/RootStore'

import { validateCharacteristicsTableData } from '../lib'
import { formatSpecificConsumptionSpreadsheetData, getCellStyle } from './lib'
import { IWerSpecificConsumptionStore } from './WerSpecificConsumptionStore.types'

const emptySpreadsheetStructure: IWerSpecificConsumptionStore['specificConsumptionSpreadsheetData'] = {
  columns: [],
  data: [],
  nestedHeaders: [],
  cell: [],
  rowHeaders: [],
}

export class WerSpecificConsumptionStore implements IWerSpecificConsumptionStore {
  rootStore: IWerSpecificConsumptionStore['rootStore']
  initialSettings: IWerSpecificConsumptionStore['initialSettings'] = null
  currentSettings: IWerSpecificConsumptionStore['currentSettings'] = null
  isSettingsSaving: IWerSpecificConsumptionStore['isSettingsSaving'] = false
  isCharacteristicsLoaded: IWerSpecificConsumptionStore['isCharacteristicsLoaded'] = false
  lastFilledRow: IWerSpecificConsumptionStore['lastFilledRow'] = null
  specificConsumptionSpreadsheetData: IWerSpecificConsumptionStore['specificConsumptionSpreadsheetData'] = {
    ...emptySpreadsheetStructure,
  }
  originalSpecificConsumptionSpreadsheetData: IWerSpecificConsumptionStore['specificConsumptionSpreadsheetData'] = {
    ...emptySpreadsheetStructure,
  }
  isEditRows: IWerSpecificConsumptionStore['isEditRows'] = false
  actualData: IWerSpecificConsumptionStore['actualData'] = []
  isActualDataLoading: IWerSpecificConsumptionStore['isActualDataLoading'] = false

  constructor(rootStore: RootStore) {
    this.rootStore = rootStore
    makeAutoObservable(this, {
      // Отключаем наблюдение, что бы ускорить инициализацию таблицы
      originalSpecificConsumptionSpreadsheetData: false,
    })
  }

  resetStore: IWerSpecificConsumptionStore['resetStore'] = () => {
    this.isCharacteristicsLoaded = false
    this.isEditRows = false
    this.actualData = []
  }

  getSpecificConsumptionCharacteristics: IWerSpecificConsumptionStore['getSpecificConsumptionCharacteristics'] = async (
    plantId: number,
    date: string,
  ) => {
    try {
      this.isCharacteristicsLoaded = false
      const data = await api.calcModelWerManager.getSpecificConsumptionCharacteristics(plantId, date)
      const formattedSpreadsheet = formatSpecificConsumptionSpreadsheetData(
        data.table,
        this.rootStore.calcModelWerStore.listOfStationsStore.characteristicsStore.editMode,
        this.rootStore.calcModelWerStore.isSelectedDateEditable,
        this.rootStore.calcModelWerStore.isSelectedPlantViewOnly,
        data.settings.pressureAllowableChange,
        data.settings.consumptionAllowableChange,
        {
          emptyValuesValidation: data.settings.emptyValuesValidation,
          allowableChangeValidation: data.settings.allowableChangeValidation,
          monotonyValidation: data.settings.monotonyValidation,
        },
      )
      runInAction(() => {
        this.currentSettings = data.settings
        this.initialSettings = klona(data.settings)
        this.lastFilledRow = data.lastFilledRow
        this.specificConsumptionSpreadsheetData = formattedSpreadsheet
        this.originalSpecificConsumptionSpreadsheetData = formattedSpreadsheet
        this.isCharacteristicsLoaded = true
      })
    } catch (error) {
      console.error('Произошла ошибка при загрузке характеристики удельного расхода ГЭС', error)
    }
  }

  saveSpecificConsumptionSettings: IWerSpecificConsumptionStore['saveSpecificConsumptionSettings'] = async (
    plantId,
    date,
    settings,
  ) => {
    try {
      const normalizedSettings = this._normalizeSettings(settings)
      if (!normalizedSettings) return

      this.isSettingsSaving = true

      await api.calcModelWerManager.saveSpecificConsumptionSettings(plantId, date, normalizedSettings)

      runInAction(() => {
        this.currentSettings = normalizedSettings
        this.initialSettings = klona(normalizedSettings)
      })
      await this.getSpecificConsumptionCharacteristics(plantId, date)
    } catch (error) {
      console.error('Произошла ошибка при сохранении настроек удельного расхода ГЭС', error)
      throw error
    } finally {
      runInAction(() => {
        this.isSettingsSaving = false
      })
    }
  }

  // Конвертация строковых значений в числовые для сравнения изменений и перед сохранением
  private readonly _normalizeSettings = (settings: IWerSpecificConsumptionStore['currentSettings']) => {
    if (!settings) return null

    const keysToConvert = ['rowCount', 'pressureAllowableChange', 'consumptionAllowableChange'] as const
    const normalizedSettings = { ...settings }

    keysToConvert.forEach((key) => {
      if (typeof normalizedSettings[key] === 'string') {
        if (key !== 'rowCount') {
          normalizedSettings[key] = normalizedSettings[key] === '' ? undefined : Number(normalizedSettings[key])
        } else {
          normalizedSettings[key] = Number(normalizedSettings[key])
        }
      }
    })

    if (Array.isArray(normalizedSettings.polynom)) {
      normalizedSettings.polynom = normalizedSettings.polynom.map((value) => {
        if (typeof value === 'string') {
          return value === '' ? null : Number(value)
        }

        return value
      })
    }

    return normalizedSettings
  }

  updateCharacteristicsSpreadsheetData: IWerSpecificConsumptionStore['updateCharacteristicsSpreadsheetData'] = (
    changes,
  ) => {
    if (!changes) return

    // После каждого изменения в таблице валидируем ее
    const errorsMap = validateCharacteristicsTableData(
      this.specificConsumptionSpreadsheetData.data,
      {
        0: this.initialSettings?.pressureAllowableChange,
        1: this.initialSettings?.consumptionAllowableChange,
      },
      {
        emptyValuesValidation: this.initialSettings?.emptyValuesValidation,
        allowableChangeValidation: this.initialSettings?.allowableChangeValidation,
        monotonyValidation: this.initialSettings?.monotonyValidation,
      },
    )

    // Состояния, от которых зависит, доступна ли таблица к редактированию
    const canEdit = this.rootStore.calcModelWerStore.listOfStationsStore.characteristicsStore.editMode
    const isSelectedDateEditable = this.rootStore.calcModelWerStore.isSelectedDateEditable
    const isSelectedPlantViewOnly = this.rootStore.calcModelWerStore.isSelectedPlantViewOnly

    // Обновляем массив cell: для каждой ячейки заново вычисляем className,
    // а также добавляем комментарий, если найдена ошибка
    const cells = this.specificConsumptionSpreadsheetData.cell
    cells.forEach((cell) => {
      const cellKey = `${cell.row}:${cell.col}`
      const message = errorsMap.get(cellKey)
      // Оптимизация. Если сообщение не изменилось, то не меняем className, comment
      if (cell.message !== message) {
        cell.className = getCellStyle({
          cell,
          canEdit,
          isSelectedDateEditable,
          isSelectedPlantViewOnly,
          message,
        })
        cell.comment = message ? getStyledComment(message) : undefined
        cell.message = message // Сохраняем для сравнения
      }
    })
    // Пересоздаём массив, чтобы изменить ссылку и MobX обнаружил изменения
    this.specificConsumptionSpreadsheetData.cell = cells.slice()

    this.isEditRows = true
  }

  saveChangedCharacteristicsSpreadsheetData: IWerSpecificConsumptionStore['saveChangedCharacteristicsSpreadsheetData'] =
    async () => {
      const plantId = this.rootStore.calcModelWerStore.selectedPlant?.plantId
      const date = this.rootStore.calcModelWerStore.formattedDate

      if (!plantId) {
        console.log('Отсутствует ID станции')

        return
      }

      try {
        this.isSettingsSaving = true

        const changesPayload = {
          matrix: this.specificConsumptionSpreadsheetData.data,
        }
        await api.calcModelWerManager.saveSpecificConsumptionCharacteristics(plantId, date, changesPayload)

        this.rootStore.notificationStore.addNotification({
          title: 'Сохранение РМ',
          description: 'Изменения сохранены',
          type: 'success',
        })

        await this.getSpecificConsumptionCharacteristics(plantId, date)
        runInAction(() => {
          this.isEditRows = false
        })
      } catch (error) {
        console.error('Ошибка при сохранении изменений характеристик водохранилища', error)
      } finally {
        runInAction(() => {
          this.isSettingsSaving = false
        })
      }
    }

  resetCharacteristicsSpreadsheetData: IWerSpecificConsumptionStore['resetCharacteristicsSpreadsheetData'] = () => {
    runInAction(() => {
      this.specificConsumptionSpreadsheetData = klona(this.originalSpecificConsumptionSpreadsheetData)
      this.isEditRows = false
    })
  }

  resetSettings: IWerSpecificConsumptionStore['resetSettings'] = () => {
    this.currentSettings = klona(this.initialSettings)
  }

  getActualData: IWerSpecificConsumptionStore['getActualData'] = async (plantId, date, startDate, endDate, signal) => {
    try {
      this.isActualDataLoading = true
      const data = await api.calcModelWerManager.getSpecificConsumptionActualMeasurements(
        plantId,
        date,
        startDate,
        endDate,
        signal,
      )
      runInAction(() => {
        this.actualData = data
      })

      this.rootStore.notificationStore.addNotification({
        title: 'Загрузка фактических данных из ИСС ГЭС',
        description: 'Загрузка выполнена',
        type: 'success',
      })
    } catch (e) {
      const error = e as AxiosError
      if (error.code !== 'ERR_CANCELED') {
        console.error('Ошибка загрузки фактических данных', error)
        // Пробрасываем ошибку для обработки в компоненте
        throw error
      }
    } finally {
      runInAction(() => {
        this.isActualDataLoading = false
      })
    }
  }

  get isSettingsModified(): IWerSpecificConsumptionStore['isSettingsModified'] {
    const normalizedCurrent = this._normalizeSettings(this.currentSettings)

    return JSON.stringify(normalizedCurrent) !== JSON.stringify(this.initialSettings)
  }

  setRowCount: IWerSpecificConsumptionStore['setRowCount'] = (rowCount) => {
    if (this.currentSettings && !Number.isNaN(rowCount)) {
      this.currentSettings.rowCount = rowCount
    }
  }

  setUnit: IWerSpecificConsumptionStore['setUnit'] = (unit) => {
    if (this.currentSettings) {
      this.currentSettings.unit = unit
    }
  }

  setPressureAllowableChange: IWerSpecificConsumptionStore['setPressureAllowableChange'] = (
    pressureAllowableChange,
  ) => {
    if (this.currentSettings && !Number.isNaN(pressureAllowableChange)) {
      this.currentSettings.pressureAllowableChange = pressureAllowableChange
    }
  }

  setConsumptionAllowableChange: IWerSpecificConsumptionStore['setConsumptionAllowableChange'] = (
    consumptionAllowableChange,
  ) => {
    if (this.currentSettings && !Number.isNaN(consumptionAllowableChange)) {
      this.currentSettings.consumptionAllowableChange = consumptionAllowableChange
    }
  }

  setEmptyValuesValidation: IWerSpecificConsumptionStore['setEmptyValuesValidation'] = (emptyValuesValidation) => {
    if (this.currentSettings) {
      this.currentSettings.emptyValuesValidation = emptyValuesValidation
    }
  }

  setAllowableChangeValidation: IWerSpecificConsumptionStore['setAllowableChangeValidation'] = (
    allowableChangeValidation,
  ) => {
    if (this.currentSettings) {
      this.currentSettings.allowableChangeValidation = allowableChangeValidation
    }
  }

  setMonotonyValidation: IWerSpecificConsumptionStore['setMonotonyValidation'] = (monotonyValidation) => {
    if (this.currentSettings) {
      this.currentSettings.monotonyValidation = monotonyValidation
    }
  }

  setPolynom: IWerSpecificConsumptionStore['setPolynom'] = (index, value) => {
    if (this.currentSettings?.polynom && !Number.isNaN(value)) {
      this.currentSettings.polynom[index] = value
    }
  }

  setMethod: IWerSpecificConsumptionStore['setMethod'] = (method) => {
    if (this.currentSettings) {
      this.currentSettings.method = method
    }
  }
}
